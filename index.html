<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>甲崩喔 Restaurant Roulette - 台南美食輪盤 | 舞鶴台南民宿</title>
    <meta name="description" content="舞鶴台南民宿推薦！隨機發現台南附近的美食餐廳，使用有趣的老虎機介面探索在地美味，支援多語言和精準定位功能。">
    <meta name="keywords" content="台南美食, 台南餐廳, 美食輪盤, 甲崩喔, 舞鶴民宿, 台南民宿, restaurant roulette, tainan food">
    
    <!-- Open Graph -->
    <meta property="og:title" content="甲崩喔 Restaurant Roulette - 台南美食輪盤">
    <meta property="og:description" content="隨機發現台南附近的美食餐廳，使用有趣的老虎機介面探索在地美味！">
    <meta property="og:image" content="https://eat.tribe.org.tw/assets/image/banner.jpg">
    <!-- 後備 Open Graph 圖片 -->
    <link rel="image_src" href="https://eat.tribe.org.tw/assets/image/banner.jpg">
    <meta property="og:image:secure_url" content="https://eat.tribe.org.tw/assets/image/banner.jpg">
    <meta property="og:image:type" content="image/jpeg">
    <meta property="og:image:width" content="1024">
    <meta property="og:image:height" content="683">
    <meta property="og:image:alt" content="甲崩喔 台南美食輪盤 - 舞鶴民宿推薦">
    <meta property="og:url" content="https://eat.tribe.org.tw/">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="甲崩喔 Restaurant Roulette">
    <meta property="og:locale" content="zh_TW">
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="甲崩喔 Restaurant Roulette - 台南美食輪盤">
    <meta name="twitter:description" content="隨機發現台南附近的美食餐廳，使用有趣的老虎機介面探索在地美味！">
    <meta name="twitter:image" content="https://eat.tribe.org.tw/assets/image/banner.jpg">
    <meta name="twitter:image:alt" content="甲崩喔 台南美食輪盤 - 舞鶴民宿推薦">
    <meta name="twitter:site" content="@tainanbnb_maizuru">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🍽️</text></svg>">

    <script src="https://resource.trickle.so/vendor_lib/unpkg/react@18/umd/react.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/@babel/standalone/babel.min.js"></script>
    <!-- 開發環境使用Tailwind Play CDN，生產環境應使用建構版本 -->
    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,line-clamp"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: 'var(--primary-color)',
              secondary: 'var(--secondary-color)',
              accent: 'var(--accent-color)'
            }
          }
        }
      }
    </script>
    <link href="https://resource.trickle.so/vendor_lib/unpkg/lucide-static@0.516.0/font/lucide.css" rel="stylesheet">
    
    <!-- Google Maps JavaScript API 將由應用程式動態載入 -->

    <!-- Predefined Tailwind CSS, use it as needed, or update base on your -->
    <style type="text/tailwindcss">
    @layer theme {
        :root {
            --primary-color: #ff6b35;
            --secondary-color: #f7931e;
            --accent-color: #ffd23f;
            --background-color: #1a1a2e;
            --surface-color: #16213e;
            --text-primary: #ffffff;
            --text-secondary: #e5e5e5;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --border-radius: 12px;
            --shadow-lg: 0 10px 25px rgba(255, 107, 53, 0.3);
        }
    }

    @layer base {
        body {
            background: linear-gradient(135deg, var(--background-color) 0%, var(--surface-color) 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            min-height: 100vh;
        }
        
        .slot-reel {
            animation: spin 2s ease-in-out;
        }
        
        @keyframes spin {
            0% { transform: translateY(0); }
            50% { transform: translateY(-200px); }
            100% { transform: translateY(0); }
        }
        
        .animate-scroll-names {
            animation: scrollNames 0.5s linear infinite;
        }

        .animate-scroll-slow-stop {
            animation: scrollSlowStop 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        @keyframes scrollNames {
            0% {
                transform: translateY(0);
            }
            100% {
                transform: translateY(-1536px); /* 6張圖片 * 256px */
            }
        }

        @keyframes scrollSlowStop {
            0% {
                transform: translateY(0);
                animation-timing-function: ease-out;
            }
            70% {
                transform: translateY(-2000px);
                animation-timing-function: ease-in;
            }
            100% {
                transform: translateY(-2304px); /* 停在餐廳圖片 */
            }
        }

        
        /* 滑桿樣式 */
        .slider {
            background: linear-gradient(to right, var(--primary-color) 0%, var(--primary-color) var(--value, 25%), #374151 var(--value, 25%), #374151 100%);
        }
        
        .slider::-webkit-slider-thumb {
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: var(--accent-color);
            cursor: pointer;
            border: 2px solid var(--primary-color);
            box-shadow: 0 2px 6px rgba(255, 107, 53, 0.3);
        }
        
        .slider::-moz-range-thumb {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: var(--accent-color);
            cursor: pointer;
            border: 2px solid var(--primary-color);
            box-shadow: 0 2px 6px rgba(255, 107, 53, 0.3);
        }
    }

    @layer components {
        .btn-primary {
            @apply bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white font-semibold py-3 px-8 rounded-[var(--border-radius)] transition-all duration-300 relative overflow-hidden;
        }
        
        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }
        
        .btn-primary:hover::before {
            left: 100%;
        }
        
        .card {
            @apply bg-[var(--surface-color)] rounded-[var(--border-radius)] p-6 border border-gray-700;
            box-shadow: var(--shadow-lg);
        }

        .glow-container {
            box-shadow: var(--shadow-lg);
        }
        
        .slot-machine {
            @apply bg-gradient-to-b from-yellow-400 to-yellow-600 rounded-2xl p-8 border-4 border-yellow-300;
            box-shadow: 0 20px 40px rgba(255, 210, 63, 0.4);
        }
    }

    @layer utilities {
        /* Define custom utility classes (e.g., .text-shadow, .skew-10) */
    }
    </style>

</head>
<body>
    <div id="root"></div>
    
    <script src="utils/locationUtils.js"></script>
    <script type="text/babel" src="components/LanguageSelector.js"></script>
    <script type="text/babel" src="components/SlotMachine.js"></script>
    <script type="text/babel" src="components/RestaurantCard.js"></script>
    <script type="text/babel" src="components/LocationManager.js"></script>
    <script type="text/babel" src="components/SearchSettings.js"></script>
    <script type="text/babel" src="components/StatusMessages.js"></script>
    <script type="text/babel" src="app.js"></script>
</body>
</html>
